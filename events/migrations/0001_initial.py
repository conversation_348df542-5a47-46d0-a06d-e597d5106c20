# Generated by Django 5.2.4 on 2025-07-08 08:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EventTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_templates', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EventStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, help_text='Custom name for this step', max_length=100)),
                ('order', models.PositiveIntegerField()),
                ('step_type', models.CharField(choices=[('GAME_PICTIONARY', '游戏：你画我猜'), ('FREE_CHAT', '自由讨论')], max_length=50)),
                ('configuration', models.JSONField(blank=True, default=dict)),
                ('duration', models.PositiveIntegerField(default=300, help_text='Duration of this step in seconds')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='events.eventtemplate')),
            ],
            options={
                'ordering': ['template', 'order'],
            },
        ),
    ]
