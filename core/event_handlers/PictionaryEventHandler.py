import random
from typing import Any, Dict, Optional, Tuple

from events.models import EventStep
from games.models import Game
from games.words import WORD_LIST
from . import BaseEventHandler
from ..models import Room
from ..utils import database_sync_to_async
from games.models import PictionaryGame, PlayerScore


# ============================================================================
# 你画我猜数据库辅助函数：
# ============================================================================

@database_sync_to_async
def get_pictionary_game(room_code):
    """获取一个房间的你画我猜游戏数据"""
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None
    
@database_sync_to_async
def end_pictionary_round(room_code):
    """结束一个房间的你画我猜回合"""
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

# All @database_sync_to_async helper functions remain the same as the last complete version.
@database_sync_to_async
def update_scores(winner, drawer, room):
    winner_score, _ = PlayerScore.objects.get_or_create(room=room, player=winner)
    winner_score.score += 10; winner_score.save()
    drawer_score, _ = PlayerScore.objects.get_or_create(room=room, player=drawer)
    drawer_score.score += 5; drawer_score.save()
    scores = PlayerScore.objects.filter(room=room).order_by('-score')
    return {score.player.username: score.score for score in scores}


class PictionaryEventHandler(BaseEventHandler):
    """你画我猜环节处理器"""

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """启动你画我猜游戏"""
        try:
            participants = list(room.participants.all())
            if len(participants) < 1:
                return None, "至少需要1名玩家才能开始游戏。"

            drawer = random.choice(participants)
            word = random.choice(WORD_LIST)

            # 更新房间状态
            room.status = Room.STATUS_IN_PROGRESS
            await database_sync_to_async(room.save)()

            # 创建游戏会话
            game_session, _ = await database_sync_to_async(
                Game.objects.update_or_create
            )(room=room, defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True})

            # 创建你画我猜游戏数据
            await database_sync_to_async(
                PictionaryGame.objects.update_or_create
            )(game=game_session, defaults={'current_word': word, 'current_drawer': drawer})

            return {
                "drawer": drawer.username,
                "word": word,
                "duration": step.duration,
                "room_status": room.status,
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None

        except Exception as e:
            self.logger.error(f"Error starting pictionary game: {e}")
            return None, "启动游戏时发生错误"

    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """处理你画我猜游戏中的消息"""
        try:
            message = payload.get('message', '').strip()
            if not message:
                return False

            # 获取当前游戏状态
            game = await get_pictionary_game(self.room_code)
            if not game:
                return False  # 不是你画我猜游戏，继续常规处理

            # 验证游戏状态
            if not game.current_word or not game.current_drawer:
                await self.send_error_to_user("游戏状态异常，请重新开始。")
                return True

            # 检查是否是绘画者（绘画者不能猜词）
            if user.id == game.current_drawer.id:
                return True  # 静默忽略绘画者的消息

            # 检查是否猜对了
            if message.lower() == game.current_word.lower():
                await self._handle_correct_guess(user, game)
                return True

            return False  # 没猜对，继续常规聊天处理

        except Exception as e:
            self.logger.error(f"Error handling pictionary message: {e}")
            return False

    async def _handle_correct_guess(self, winner, game):
        """处理正确猜词"""
        try:
            # 更新分数
            updated_scores = await update_scores(
                winner=winner,
                drawer=game.current_drawer,
                room=game.game.room
            )

            # 结束回合
            new_room_status = await end_pictionary_round(self.room_code)

            if new_room_status:
                await self.broadcast_to_room('broadcast_round_over', {
                    'winner': winner.username,
                    'word': game.current_word,
                    'room_status': new_room_status,
                    'scores': updated_scores,
                })

        except Exception as e:
            self.logger.error(f"Error handling correct guess: {e}")

    async def handle_timeout(self) -> None:
        """处理你画我猜超时"""
        try:
            game = await get_pictionary_game(self.room_code)
            if game:
                new_room_status = await end_pictionary_round(self.room_code)
                if new_room_status:
                    await self.broadcast_to_room('broadcast_round_over', {
                        'winner': None,  # 超时无获胜者
                        'word': game.current_word,
                        'room_status': new_room_status,
                        'scores': {},  # 超时不更新分数
                        'timeout': True,
                    })
        except Exception as e:
            self.logger.error(f"Error handling pictionary timeout: {e}")

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """处理你画我猜重启"""
        try:
            room = await self.get_room_with_template()
            if not room:
                return None, '房间不存在。'

            # 获取当前步骤
            current_step = await database_sync_to_async(
                lambda: room.event_template.steps.filter(order=room.current_step_order).first()
            )()

            if not current_step:
                return None, '无法找到当前环节信息。'

            # 重新启动游戏
            return await self.start_step(room, current_step)

        except Exception as e:
            self.logger.error(f"Error restarting pictionary: {e}")
            return None, "重新开始游戏时发生错误。"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """处理你画我猜的自定义动作"""
        if action == 'send_drawing':
            return await self._handle_drawing_data(user, payload)
        return False

    async def _handle_drawing_data(self, user, payload: Dict[str, Any]) -> bool:
        """处理绘图数据"""
        try:
            path_data = payload.get('path_data')
            if not path_data:
                return True

            # 验证路径数据的有效性
            if not isinstance(path_data, dict) or not path_data.get('path') or not path_data.get('id'):
                self.logger.warning(f"Invalid path data received from user {user.username}")
                return True

            # 验证用户是否有绘画权限
            game = await get_pictionary_game(self.room_code)
            if game and user.id != game.current_drawer.id:
                await self.send_error_to_user("只有绘画者可以绘画。")
                return True

            # 路径长度检查（避免过长的路径导致性能问题）
            path_str = path_data.get('path', '')
            if len(path_str) > 10000:  # 限制路径长度
                self.logger.warning(f"Path too long from user {user.username}: {len(path_str)} characters")
                return True

            # 广播绘图数据
            await self.broadcast_to_room('broadcast_drawing_data', {
                'path_data': {
                    'id': path_data['id'],
                    'path': path_str,
                    'color': path_data.get('color', 'black')
                }
            })
            return True

        except Exception as e:
            self.logger.error(f"Error handling drawing data: {e}")
            return True