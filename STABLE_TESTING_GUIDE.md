# 环节设计器V2 稳定版测试指南

## 🎯 本次更新内容

基于稳定性考虑，本次更新采用了保守的实现方式：

### ✅ 已实现功能

1. **步骤删除功能**
   - 在EditStepScreen中添加删除按钮
   - 包含确认对话框防止误删
   - 完整的权限验证

2. **步骤排序功能**
   - 使用上下移动按钮进行排序
   - 避免了可能导致闪退的拖拽库
   - 实时API同步

3. **界面优化**
   - 改进的步骤卡片设计
   - 更好的空状态显示
   - 清晰的视觉层次

### 🛡️ 稳定性保证

- **仅使用React Native原生组件**
- **避免引入第三方动画库**
- **保持现有项目结构不变**
- **向后兼容所有现有功能**

## 📱 测试步骤

### 1. 基础功能测试

#### 启动应用
```bash
# 后端
cd Tuanzi && source venv/bin/activate && python manage.py runserver

# 前端
cd TuanziApp && npm start
```

#### 验证应用启动
- ✅ 应用正常启动，无闪退
- ✅ 登录功能正常
- ✅ 导航正常工作

### 2. 删除功能测试

#### 测试步骤：
1. 进入环节设计器
2. 选择一个有步骤的模板
3. 点击任意步骤进入编辑页面
4. 滚动到底部，点击"删除步骤"按钮
5. 确认删除对话框
6. 验证步骤被删除

#### 预期结果：
- ✅ 删除按钮正常显示
- ✅ 确认对话框正常弹出
- ✅ 删除操作成功执行
- ✅ 返回列表页面，步骤已消失

### 3. 排序功能测试

#### 测试步骤：
1. 在模板详情页面查看步骤列表
2. 找到步骤卡片右侧的上下箭头按钮
3. 点击向上箭头移动步骤
4. 点击向下箭头移动步骤
5. 验证顺序变化

#### 预期结果：
- ✅ 上下移动按钮正常显示
- ✅ 第一个步骤的向上按钮为禁用状态
- ✅ 最后一个步骤的向下按钮为禁用状态
- ✅ 点击按钮后步骤顺序立即更新
- ✅ 刷新页面后顺序保持不变

### 4. 界面优化验证

#### 步骤卡片：
- ✅ 新的徽章式序号显示
- ✅ 步骤名称和类型分行显示
- ✅ 时长信息突出显示
- ✅ 编辑提示清晰可见

#### 空状态：
- ✅ 友好的图标和文案
- ✅ 引导用户添加步骤
- ✅ 视觉层次清晰

## 🧪 API测试

### 后端测试
```bash
cd Tuanzi
source venv/bin/activate
python manage.py test events.test_api_v2 -v 2
```

**预期结果**: 所有12个测试通过

### 手动API测试
```bash
# 获取token
TOKEN=$(curl -X POST http://localhost:8000/api/token/ \
  -H "Content-Type: application/json" \
  -d '{"username":"your_username","password":"your_password"}' \
  | jq -r '.access')

# 测试删除步骤
curl -X DELETE http://localhost:8000/api/events/steps/STEP_ID/ \
  -H "Authorization: Bearer $TOKEN"

# 测试重新排序
curl -X POST http://localhost:8000/api/events/templates/TEMPLATE_ID/reorder-steps/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"step_ids": [3, 1, 2]}'
```

## 🔍 故障排除

### 如果应用闪退：
1. 检查Metro服务器日志
2. 查看设备日志：`npx react-native log-android`
3. 清理缓存：`npx react-native start --reset-cache`

### 如果功能不工作：
1. 确认后端服务器正在运行
2. 检查网络连接
3. 验证用户权限

### 如果样式异常：
1. 重新加载应用
2. 检查是否有TypeScript错误
3. 确认所有样式定义正确

## 📊 性能验证

### 内存使用：
- ✅ 应用内存使用稳定
- ✅ 无内存泄漏
- ✅ 长时间使用无异常

### 响应速度：
- ✅ 界面操作响应迅速
- ✅ API调用延迟正常
- ✅ 动画流畅自然

## 🚀 下一步计划

### 可选的未来改进：
1. **拖拽排序**: 等待更稳定的拖拽库版本
2. **更多动画**: 在确保稳定性的前提下添加
3. **批量操作**: 批量删除、移动等功能
4. **快捷操作**: 长按菜单等高级交互

### 建议的开发顺序：
1. 先完成其他团队成员的功能（签到、货币、点名）
2. 在稳定版本基础上逐步添加高级功能
3. 充分测试每个新功能的稳定性

## 📝 测试报告模板

**测试日期**: ___________
**测试环境**: ___________
**测试人员**: ___________

| 功能 | 状态 | 备注 |
|------|------|------|
| 应用启动 | ✅/❌ | |
| 步骤删除 | ✅/❌ | |
| 步骤排序 | ✅/❌ | |
| 界面优化 | ✅/❌ | |
| API功能 | ✅/❌ | |

**总体评价**: ___________
**发现问题**: ___________
**改进建议**: ___________
