import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { testNetworkConnection, testLoginAPI } from '../utils/networkTest';
import { useNavigation } from '@react-navigation/native';

export const LoginScreen = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login } = useAuth();
  const navigation = useNavigation<any>();

  const handleLogin = async () => {
    const success = await login(username, password);
    if (!success) {
      Alert.alert("登录失败", "请检查您的用户名和密码。");
    }
  };

  const handleNetworkTest = async () => {
    Alert.alert("网络测试", "开始测试网络连接...");

    // 测试基本连接
    const connectionResult = await testNetworkConnection();
    console.log('连接测试结果:', connectionResult);

    if (!connectionResult.success) {
      Alert.alert("网络测试失败", `连接失败: ${connectionResult.message}`);
      return;
    }

    // 测试登录API
    const loginResult = await testLoginAPI('test', 'test');
    console.log('登录测试结果:', loginResult);

    const message = `连接测试: ${connectionResult.success ? '✅' : '❌'}\n登录测试: ${loginResult.success ? '✅' : '❌'}`;
    Alert.alert("网络测试结果", message);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>登录团子</Text>
      <TextInput style={styles.input} placeholder="用户名" value={username} onChangeText={setUsername} autoCapitalize="none" />
      <TextInput style={styles.input} placeholder="密码" value={password} onChangeText={setPassword} secureTextEntry />
      <Button title="登录" onPress={handleLogin} />
      <View style={styles.buttonSpacing} />
      <Button title="🔧 网络测试" onPress={handleNetworkTest} color="#666" />
      <TouchableOpacity onPress={() => navigation.navigate('Register')}>
          <Text style={styles.linkText}>还没有账户？点击注册</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
    title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 },
    input: { width: '100%', height: 40, borderColor: 'gray', borderWidth: 1, borderRadius: 5, marginBottom: 12, paddingHorizontal: 10 },
    buttonSpacing: { height: 10 },
    linkText: { color: 'blue', marginTop: 15 }
});
