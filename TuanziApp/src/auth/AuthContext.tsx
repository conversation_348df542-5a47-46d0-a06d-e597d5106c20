import React, { createContext, useState, useContext, ReactNode } from 'react';
import { API_URL } from '../api/client';
import * as authStorage from './storage';
// No need to import jwt-decode here anymore as it's handled in storage.ts

interface User {
  username: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  restoreUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      console.log('Attempting login with URL:', `${API_URL}/api/token/`);
      console.log('Login data:', { username, password: '***' });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const response = await fetch(`${API_URL}/api/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('Response status:', response.status);
      const data = await response.json();
      console.log('Response data:', data);

      if (response.ok && data.access) {
        // storage.ts now handles decoding
        await authStorage.storeToken(data.access);
        setToken(data.access);
        setUser({ username });
        console.log('Login successful');
        return true;
      }
      console.log('Login failed: Invalid response or missing access token');
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    authStorage.removeToken();
    setUser(null);
    setToken(null);
  };

  const restoreUser = async () => {
    const userData = await authStorage.getUser();
    if (userData) {
      setUser({ username: userData.username });
      setToken(userData.token);
    }
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, restoreUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};