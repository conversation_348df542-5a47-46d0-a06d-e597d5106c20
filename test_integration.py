#!/usr/bin/env python3
"""
集成测试脚本 - 测试重构后的环节处理器系统
"""
import asyncio
import websockets
import json
import requests
import sys
import os

# 添加项目路径
sys.path.append('/home/<USER>/Desktop/program/project/Tuanzi')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')

import django
django.setup()

from django.contrib.auth import get_user_model
from core.models import Room
from events.models import EventTemplate, EventStep
from channels.db import database_sync_to_async

User = get_user_model()

BASE_URL = "http://localhost:8001"
WS_URL = "ws://localhost:8001"

class IntegrationTest:
    def __init__(self):
        self.token = None
        self.room_code = None
        self.template_id = None
        
    def setup_test_data_sync(self):
        """设置测试数据（同步版本）"""
        print("🔧 设置测试数据...")

        # 创建测试用户
        try:
            user = User.objects.get(username='testuser')
        except User.DoesNotExist:
            user = User.objects.create_user(
                username='testuser',
                password='testpass123'
            )

        # 创建测试模板
        template, _ = EventTemplate.objects.get_or_create(
            name='集成测试模板',
            creator=user,
            defaults={'description': '用于集成测试的模板'}
        )

        # 清除旧的步骤
        template.steps.all().delete()

        # 创建测试步骤
        EventStep.objects.create(
            template=template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=60,  # 1分钟用于测试
            name='测试你画我猜'
        )

        EventStep.objects.create(
            template=template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=30,  # 30秒用于测试
            name='测试自由聊天'
        )

        self.template_id = template.id
        print(f"✅ 测试数据设置完成，模板ID: {self.template_id}")

    async def setup_test_data(self):
        """设置测试数据（异步包装）"""
        await database_sync_to_async(self.setup_test_data_sync)()
        
    async def login(self):
        """登录获取token"""
        print("🔐 登录获取token...")
        
        response = requests.post(f"{BASE_URL}/api/token/", {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        if response.status_code == 200:
            self.token = response.json()['access']
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    
    async def create_room(self):
        """创建房间"""
        print("🏠 创建房间...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.post(f"{BASE_URL}/api/rooms/create/", {
            'template_id': self.template_id
        }, headers=headers)
        
        if response.status_code == 201:
            self.room_code = response.json()['room_code']
            print(f"✅ 房间创建成功: {self.room_code}")
            return True
        else:
            print(f"❌ 房间创建失败: {response.status_code}")
            return False
    
    async def test_websocket_connection(self):
        """测试WebSocket连接和环节处理"""
        print("🔌 测试WebSocket连接...")
        
        uri = f"{WS_URL}/ws/room/{self.room_code}/?token={self.token}"
        
        try:
            async with websockets.connect(uri) as websocket:
                print("✅ WebSocket连接成功")
                
                # 测试启动第一个环节（你画我猜）
                await self.test_pictionary_step(websocket)
                
                # 等待一下
                await asyncio.sleep(2)
                
                # 测试启动第二个环节（自由聊天）
                await self.test_free_chat_step(websocket)
                
                return True
                
        except Exception as e:
            print(f"❌ WebSocket测试失败: {e}")
            return False
    
    async def test_pictionary_step(self, websocket):
        """测试你画我猜环节"""
        print("🎨 测试你画我猜环节...")
        
        # 发送启动下一环节的消息
        await websocket.send(json.dumps({
            'action': 'next_step',
            'payload': {}
        }))
        
        # 接收响应
        response = await websocket.recv()
        data = json.loads(response)
        
        print(f"📨 收到消息: {data['type']}")
        
        if data['type'] == 'step_started':
            payload = data['payload']
            if payload.get('step_info', {}).get('step_type') == 'GAME_PICTIONARY':
                print("✅ 你画我猜环节启动成功")
                print(f"   绘画者: {payload.get('drawer', 'N/A')}")
                print(f"   词汇: {payload.get('word', 'N/A')}")
                
                # 测试发送聊天消息（猜词）
                await websocket.send(json.dumps({
                    'action': 'send_message',
                    'payload': {'message': '测试消息'}
                }))
                
                # 测试发送绘图数据
                await websocket.send(json.dumps({
                    'action': 'send_drawing',
                    'payload': {
                        'path_data': {
                            'id': 'test1',
                            'path': 'M10,10 L20,20',
                            'color': '#000000'
                        }
                    }
                }))
                
                return True
            else:
                print("❌ 环节类型不正确")
                return False
        else:
            print(f"❌ 收到意外消息类型: {data['type']}")
            return False
    
    async def test_free_chat_step(self, websocket):
        """测试自由聊天环节"""
        print("💬 测试自由聊天环节...")
        
        # 发送启动下一环节的消息
        await websocket.send(json.dumps({
            'action': 'next_step',
            'payload': {}
        }))
        
        # 接收响应
        response = await websocket.recv()
        data = json.loads(response)
        
        print(f"📨 收到消息: {data['type']}")
        
        if data['type'] == 'step_started':
            payload = data['payload']
            if payload.get('step_info', {}).get('step_type') == 'FREE_CHAT':
                print("✅ 自由聊天环节启动成功")
                
                # 测试发送聊天消息
                await websocket.send(json.dumps({
                    'action': 'send_message',
                    'payload': {'message': '大家好！这是自由聊天测试'}
                }))
                
                return True
            else:
                print("❌ 环节类型不正确")
                return False
        else:
            print(f"❌ 收到意外消息类型: {data['type']}")
            return False
    
    async def run_test(self):
        """运行完整的集成测试"""
        print("🚀 开始集成测试...")
        
        try:
            # 设置测试数据
            await self.setup_test_data()
            
            # 登录
            if not await self.login():
                return False
            
            # 创建房间
            if not await self.create_room():
                return False
            
            # 测试WebSocket
            if not await self.test_websocket_connection():
                return False
            
            print("🎉 集成测试全部通过！")
            return True
            
        except Exception as e:
            print(f"❌ 集成测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主函数"""
    test = IntegrationTest()
    success = await test.run_test()
    
    if success:
        print("\n✅ 重构成功！面向对象的环节处理器系统正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 重构存在问题，需要进一步调试。")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
